/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    CollectPopup: typeof import('./src/components/collect/CollectPopup.vue')['default']
    CountDownTimer: typeof import('./src/components/count-down-timer/CountDownTimer.vue')['default']
    Icon: typeof import('./src/components/common/Icon.vue')['default']
    PhysicalExaminationSummary: typeof import('./src/components/physical-examination-summary/PhysicalExaminationSummary.vue')['default']
    PolicyMatch: typeof import('./src/components/policy-match/PolicyMatch.vue')['default']
    ProductMatch: typeof import('./src/components/product-match/ProductMatch.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SeekHelp: typeof import('./src/components/common/SeekHelp.vue')['default']
    SpecialInspection: typeof import('./src/components/special-inspection/SpecialInspection.vue')['default']
    SpecialInspectionDetail: typeof import('./src/components/special-inspection-detail/SpecialInspectionDetail.vue')['default']
    VanButton: typeof import('vant/es')['Button']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanCountDown: typeof import('vant/es')['CountDown']
    VanDatePicker: typeof import('vant/es')['DatePicker']
    VanDivider: typeof import('vant/es')['Divider']
    VanEmpty: typeof import('vant/es')['Empty']
    VanField: typeof import('vant/es')['Field']
    VanForm: typeof import('vant/es')['Form']
    VanIcon: typeof import('vant/es')['Icon']
    VanImage: typeof import('vant/es')['Image']
    VanImagePreview: typeof import('vant/es')['ImagePreview']
    VanList: typeof import('vant/es')['List']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanOverlay: typeof import('vant/es')['Overlay']
    VanPicker: typeof import('vant/es')['Picker']
    VanPopover: typeof import('vant/es')['Popover']
    VanPopup: typeof import('vant/es')['Popup']
    VanPullRefresh: typeof import('vant/es')['PullRefresh']
    VanSearch: typeof import('vant/es')['Search']
    VanSkeleton: typeof import('vant/es')['Skeleton']
    VanStepper: typeof import('vant/es')['Stepper']
    VanSticky: typeof import('vant/es')['Sticky']
    VanSwipe: typeof import('vant/es')['Swipe']
    VanSwipeItem: typeof import('vant/es')['SwipeItem']
    VanTab: typeof import('vant/es')['Tab']
    VanTabbar: typeof import('vant/es')['Tabbar']
    VanTabbarItem: typeof import('vant/es')['TabbarItem']
    VanTabs: typeof import('vant/es')['Tabs']
    VanTag: typeof import('vant/es')['Tag']
    VanUploader: typeof import('vant/es')['Uploader']
  }
}
