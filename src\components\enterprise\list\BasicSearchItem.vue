<script lang="ts" setup>
import type { ICompanyInfo } from '@/types/company'
import CICON from '@/assets/hub-images/company/company-icon.png'
import Icon from '@/components/common/Icon.vue'
import { computed } from 'vue'
import type { RootState } from '@/types/store'
import { useStore } from 'vuex'
import openService from '@/service/openService'

const props = defineProps<{
    data: ICompanyInfo
    openActionSheet: (companyInfo: ICompanyInfo) => void
    scope: string
}>()

const store = useStore<RootState>()
const { account } = store.state.user || {}
const { user } = account || {}

const isCantransfer = computed(() => {
    return !props.data.isBuy && props.data.socialCreditCode
})

const isShowBaseInfo = computed(() => {
    return props.scope !== 'personnel'
})

const isShowIntro = computed(() => {
    return props.scope !== 'personnel'
})

const isShowPersonnelInfo = computed(() => {
    return props.scope === 'personnel'
})

const isShowSite = computed(() => {
    return props.scope === 'personnel'
})

const isShowAddress = computed(() => {
    return props.scope === 'personnel'
})

const toJyhyUrl = () => {
    if (user?.id && user?.tenantId) {
        const path = `/my/companydetail?socialCreditCode=${props.data.socialCreditCode}&companyName=${props.data.name}`

        openService
            .ssoAuthentication({
                params: {
                    to: path,
                },
                redirectUrl: import.meta.env.VITE_APP_JYHY_URL + '/stoken',
                tenantId: user.tenantId,
                userId: user.id,
            })
            .then((res) => {
                const { redirectUrl } = res
                if (redirectUrl) {
                    // 单页跳转
                    window.location.href = redirectUrl
                }
            })
    }
}
</script>

<template>
    <div class="flex flex-column back-color-white all-padding-12 border-radius-8 gap-8">
        <div class="flex flex-row gap-8">
            <div class="h-48 w-48">
                <img :src="CICON" alt="" class="height-100 wight-100" />
            </div>
            <div class="flex flex-column gap-4">
                <div class="font-16 color-black" v-html="data.companyname_ws"></div>
                <div class="font-16 flex flex-row gap-4 oh">
                    <div class="small-tag tag-green">
                        {{ data.entstatus.slice(0, 2) }}
                    </div>
                    <template v-for="(value, index) in data.companyTags" :key="index">
                        <div class="small-tag tag-gold" v-if="index === 1">
                            {{ value.tagName }}
                        </div>
                        <div class="small-tag tag-blue" v-if="index > 1 && index < 3">
                            {{ value.tagName }}
                        </div>
                    </template>
                    <div class="small-tag tag-blue" v-if="data.companyTags.length > 3">...</div>
                </div>
            </div>
        </div>
        <div class="font-14" v-if="isShowBaseInfo">
            <span class="color-blue">{{ data.legalperson || '-' }}</span
            >｜{{ data.regCapDisplay || '-' }}｜{{ data.esdate || '-' }}
        </div>
        <div class="font-14 text-ellipsis text-nowrap" v-if="isShowIntro">简介：{{ data.opscope || '-' }}</div>
        <div
            class="font-14 text-ellipsis text-nowrap remove-em color-blue"
            v-html="data.personnel_info || '-'"
            v-if="isShowPersonnelInfo"
        ></div>

        <div class="font-14 text-ellipsis text-nowrap" v-if="isShowSite">网址：{{ data.officialWebsite || '-' }}</div>
        <div class="font-14" v-if="isShowAddress">通讯地址：{{ data.contactaddress || '-' }}</div>
        <div class="h-1 back-color-border width-100"></div>
        <div class="flex flex-row font-14 space-around color-blue">
            <div class="flex flex-row top-bottom-center gap-2">
                <Icon icon="icon-a-huaban273" color="var(--main-blue-)" :size="14" @click="toJyhyUrl()" />
                体检
            </div>
            <div class="back-color-border w-1"></div>
            <div class="flex flex-row top-bottom-center gap-2">
                <Icon icon="icon-a-huaban273" color="var(--main-blue-)" :size="14" />
                数电
            </div>
            <div class="back-color-border w-1" v-if="isCantransfer"></div>
            <div class="flex flex-row top-bottom-center gap-2" v-if="isCantransfer" @click="openActionSheet(data)">
                <Icon icon="icon-a-huaban273" color="var(--main-blue-)" :size="14" />
                转移
            </div>
        </div>
    </div>
</template>

<style scoped></style>
