<template>
    <div class="home tb-padding-12 lr-padding-16 border-box" style="background-color: #F2F5F8">
        <div class="display-flex space-between top-bottom-center">
            <van-field class="border-radius-8" v-model="searchValue" placeholder="请输入模板名称查询" size="normal"/>
            <div class="border-radius-8 flex-center color-white l-margin-8" @click="onClickButton" style="background-color: #2B83FD;width: 1.8rem;height: 1rem;">
                <span class="font-14">搜索</span>
            </div>
        </div>
        <div class="display-flex top-bottom-center font-14 tb-margin-12">
            产业模板
            <span class="l-margin-4 color-blue">{{ totalNum }}条</span>
        </div>
        <van-list v-model:loading="loading" :finished="finished" finished-text="暂无数据" @load="onLoad">
            <div class="van-list-wrapper">
                <van-cell v-for="item in templateList" :key="item.id" class="list-item">
                    <div class="font-14" style="color:#222222;text-align: left;">{{ item.name }}</div>
                </van-cell>
            </div>
        </van-list>
    </div>
</template>

<script lang='ts' setup>
import { ref, computed, onMounted, watch } from 'vue'
import { tabbarheight } from '@/utils/tabbar-height'
import aicService from '@/service/aicService'
import type { ISearchGetTemplateParams, ISearchGetTemplateItem } from '@/types/company'

const totalNum = ref(0)
const searchValue = ref('')
const queryParams = ref<ISearchGetTemplateParams>({
    page: 1,
    pageSize: 20,
    templateName:''
})
watch( searchValue, (val) => {
    queryParams.value.templateName = val
})

const onClickButton = () => {  
    finished.value = false
    queryParams.value.page = 1
    templateList.value = []
    onLoad()
}

const loading = ref<boolean>(false)
const finished = ref<boolean>(false)
const templateList = ref<ISearchGetTemplateItem[]>([])
let ti = null
const onLoad = () => { 
    if (ti) {
        clearTimeout(ti)
    }
    ti = setTimeout(async () => {
        const res = await search()
        queryParams.value.page += 1
        loading.value = false
        console.log('benefitList.value.length',templateList.value.length)
        if (templateList.value.length === res.total) {
            finished.value = true
        }
    },100)
}

const paddingBottom = computed(() => {
    return tabbarheight() + 'px'
})

const search = async () => {
    console.log('queryParams.value',queryParams.value)
    const res = await aicService.searchGetTemplate(queryParams.value)
    templateList.value.push(...res.data)
    totalNum.value = res.total
    return res
}

onMounted(() => {
})

</script>

<style lang='scss' scoped>
.home {
    height: 100%;
    overflow: scroll;
    padding-bottom: v-bind(paddingBottom);
    display: flex;
    flex-direction: column;
}

.van-search__input {
  background-color: white !important; 
}

.van-list-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: space-between;
}

.list-item {
    width: calc(50% - 4px);
    border-radius: 8px;
    box-sizing: border-box;
    height: 100px;
}
</style>